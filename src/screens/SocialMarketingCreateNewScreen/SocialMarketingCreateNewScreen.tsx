import SocialMarketingCreateNew from 'features/socialMarketing/components/SocialMarketingCreateNew';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';
import { country } from 'utils/context';

export default function SocialMarketingCreateNewScreen() {
  switch (country) {
    case 'ib':
    case 'id':
    case 'my':
    case 'ph':
      return <SocialMarketingCreateNew />;
    default:
      return <NotFoundScreen />;
  }
}
