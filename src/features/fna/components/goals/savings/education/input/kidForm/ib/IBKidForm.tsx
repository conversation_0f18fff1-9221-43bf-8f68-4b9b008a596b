import DeviceBasedRendering from 'components/DeviceBasedRendering';
import type { InternalKidFormProps } from '../KidForm';
import IBKidFormTablet from './IBKidForm.tablet';
import IBKidFormPhone from './IBKidForm.phone';
import { useEffect, useState } from 'react';
import {
  IB_DEFAULT_EDUCATION_TARGET_AMOUNT,
  UNIVERSITY_AGE,
} from 'features/fna/constants/goalCalculation';
import { nameSchema } from 'features/fna/utils/validation/fnaValidation';
import { TFuncKey } from 'i18next';
import { useTranslation } from 'react-i18next';
import { Gender } from 'types/person';

export interface IBKidFormProps extends InternalKidFormProps {
  nameError: string | undefined;
  onChangeName: (name: string) => void;
  onFocusName: () => void;
  onBlurName: () => void;
  onChangeGender: (gender: string) => void;
  onChangeAge: (age: string) => void;
  onBlurAge: () => void;
  onChangeYearsToAchieve: (value: number | null) => void;
  onChangeTargetAmount: (value: number | null) => void;
}

export default function IBKidForm(props: InternalKidFormProps) {
  const { t } = useTranslation(['fna']);

  const kidGoal = props.formData.goals[props.index];

  const [nameError, setNameError] = useState<string | undefined>(undefined);
  const onChangeName = (value: string) => {
    props.updateFormData({
      ...kidGoal,
      firstName: value,
    });
  };
  const onFocusName = () => {
    setNameError(undefined);
  };
  const onBlurName = () => {
    if (kidGoal.firstName.length === 0) return;
    try {
      nameSchema.validateSync(kidGoal.firstName);
    } catch (e) {
      setNameError(t((e as Error).message as TFuncKey<['fna']>));
    }
  };

  const onChangeGender = (gender: string) => {
    props.updateFormData({
      ...kidGoal,
      gender: gender as Gender,
    });
  };

  const onChangeAge = (value: string) => {
    if (value.length === 0) {
      props.updateFormData({
        ...kidGoal,
        childAge: 0,
      });
    } else {
      const childAge = parseInt(value, 10);
      if (Number.isInteger(childAge)) {
        props.updateFormData({
          ...kidGoal,
          childAge,
        });
      }
    }
  };
  const onBlurAge = () => {
    props.updateFormData({
      ...kidGoal,
      yearsToAchieve: Math.max(0, UNIVERSITY_AGE - (kidGoal.childAge ?? 0)),
    });
  };

  const onChangeYearsToAchieve = (yearsToAchieve: number | null) => {
    props.updateFormData(
      {
        ...kidGoal,
        yearsToAchieve: yearsToAchieve,
      },
      true,
    );
  };

  const onChangeTargetAmount = (targetAmount: number | null) => {
    props.updateFormData(
      {
        ...kidGoal,
        targetAmount: targetAmount,
      },
      true,
    );
  };

  return (
    <DeviceBasedRendering
      tablet={
        <IBKidFormTablet
          {...props}
          nameError={nameError}
          onChangeName={onChangeName}
          onFocusName={onFocusName}
          onBlurName={onBlurName}
          onChangeGender={onChangeGender}
          onChangeAge={onChangeAge}
          onBlurAge={onBlurAge}
          onChangeYearsToAchieve={onChangeYearsToAchieve}
          onChangeTargetAmount={onChangeTargetAmount}
        />
      }
      phone={
        <IBKidFormPhone
          {...props}
          nameError={nameError}
          onChangeName={onChangeName}
          onFocusName={onFocusName}
          onBlurName={onBlurName}
          onChangeGender={onChangeGender}
          onChangeAge={onChangeAge}
          onBlurAge={onBlurAge}
          onChangeYearsToAchieve={onChangeYearsToAchieve}
          onChangeTargetAmount={onChangeTargetAmount}
        />
      }
    />
  );
}
