import * as React from 'react';
import Svg, { Path } from 'react-native-svg';
import { SvgIconProps } from 'cube-ui-components';

export default function BOInAppSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 38}
      height={props.height || 24}
      viewBox="0 0 38 24"
      fill="none">
      <Path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M13.334 3.5C13.334 2.67 12.664 2 11.834 2H2.83398C2.00398 2 1.33398 2.67 1.33398 3.5V16.5C1.33398 17.33 2.00398 18 2.83398 18H3.33398V4H13.334V3.5ZM8.41398 20H6.33398V7H15.334V8.25C15.334 9.08 16.004 9.75 16.834 9.75H17.334V6.5C17.334 5.67 16.664 5 15.834 5H5.83398C5.00398 5 4.33398 5.67 4.33398 6.5V20.5C4.33398 21.33 5.00398 22 5.83398 22H6.91398C7.74398 22 8.41398 21.33 8.41398 20.5V20Z"
        fill="#183028"
      />
      <Path
        d="M15.1443 18.4502C15.0743 18.7102 14.9543 18.9602 14.8043 19.2002C14.6543 19.4402 14.4543 19.6402 14.2143 19.8202C13.9743 20.0002 13.7043 20.1402 13.3843 20.2402C13.0643 20.3402 12.7143 20.3902 12.3343 20.3902C11.9143 20.3902 11.5143 20.3202 11.1343 20.1702C10.7443 20.0202 10.4043 19.8102 10.1143 19.5302C9.8243 19.2502 9.5843 18.9102 9.4143 18.5102C9.2443 18.1102 9.1543 17.6502 9.1543 17.1302C9.1543 16.6102 9.2343 16.1902 9.4043 15.8002C9.5743 15.4002 9.7943 15.0602 10.0843 14.7902C10.3643 14.5102 10.6943 14.3002 11.0643 14.1402C11.4343 13.9902 11.8143 13.9102 12.2043 13.9102C13.1543 13.9102 13.9043 14.1802 14.4443 14.7302C14.9943 15.2802 15.2643 16.0502 15.2643 17.0402C15.2643 17.1702 15.2643 17.3002 15.2543 17.4202C15.2543 17.5402 15.2343 17.6102 15.2343 17.6202H10.9843C10.9843 17.7902 11.0343 17.9502 11.1143 18.0902C11.1943 18.2302 11.2943 18.3602 11.4243 18.4602C11.5543 18.5702 11.6943 18.6502 11.8643 18.7102C12.0343 18.7702 12.2043 18.8002 12.3843 18.8002C12.7243 18.8002 12.9943 18.7302 13.1943 18.5802C13.3843 18.4302 13.5243 18.2402 13.6143 18.0002L15.1743 18.4402L15.1443 18.4502ZM13.4443 16.4202C13.4443 16.3102 13.4143 16.1902 13.3643 16.0602C13.3243 15.9402 13.2443 15.8302 13.1443 15.7302C13.0443 15.6302 12.9243 15.5502 12.7643 15.4802C12.6143 15.4102 12.4243 15.3802 12.2043 15.3802C12.0043 15.3802 11.8343 15.4102 11.6843 15.4802C11.5343 15.5502 11.4143 15.6302 11.3043 15.7302C11.2043 15.8302 11.1243 15.9402 11.0743 16.0602C11.0243 16.1802 10.9943 16.3002 10.9843 16.4202H13.4343H13.4443Z"
        fill="#183028"
      />
      <Path
        d="M21.9652 18.4403H18.5152L17.8752 20.2503H15.7852L19.1452 11.3203H21.4452L24.7652 20.2503H22.5952L21.9552 18.4403H21.9652ZM19.1752 16.6303H21.3452L20.2752 13.5403L19.1752 16.6303Z"
        fill="#183028"
      />
      <Path
        d="M23.7344 22.642V13.992H25.5844V14.632C25.7144 14.432 25.9444 14.252 26.2644 14.082C26.5844 13.912 26.9744 13.832 27.4244 13.832C27.8744 13.832 28.2844 13.912 28.6444 14.082C29.0044 14.242 29.3144 14.472 29.5544 14.762C29.8044 15.052 29.9944 15.392 30.1244 15.792C30.2544 16.192 30.3244 16.622 30.3244 17.092C30.3244 17.562 30.2544 18.012 30.1044 18.412C29.9544 18.812 29.7544 19.162 29.4844 19.452C29.2244 19.742 28.9044 19.972 28.5444 20.132C28.1744 20.292 27.7744 20.382 27.3444 20.382C26.9444 20.382 26.5944 20.322 26.3044 20.202C26.0144 20.082 25.7944 19.932 25.6444 19.752V22.632H23.7344V22.642ZM28.4544 17.112C28.4544 16.852 28.4144 16.622 28.3444 16.432C28.2744 16.242 28.1644 16.082 28.0244 15.942C27.8944 15.812 27.7344 15.712 27.5644 15.652C27.3944 15.592 27.2144 15.562 27.0344 15.562C26.8544 15.562 26.6744 15.592 26.4944 15.652C26.3144 15.712 26.1744 15.812 26.0444 15.942C25.9144 16.072 25.8144 16.232 25.7344 16.432C25.6544 16.622 25.6244 16.852 25.6244 17.112C25.6244 17.372 25.6644 17.602 25.7344 17.792C25.8144 17.982 25.9144 18.142 26.0444 18.282C26.1744 18.422 26.3244 18.512 26.4944 18.582C26.6644 18.652 26.8444 18.682 27.0344 18.682C27.2244 18.682 27.3944 18.652 27.5644 18.592C27.7344 18.532 27.8944 18.432 28.0244 18.302C28.1544 18.172 28.2644 18.012 28.3444 17.812C28.4244 17.612 28.4544 17.382 28.4544 17.122V17.112Z"
        fill="#183028"
      />
      <Path
        d="M29.7539 22.6381V13.9881H31.6039V14.6281C31.7339 14.4281 31.9639 14.2481 32.2839 14.0781C32.6039 13.9081 32.9939 13.8281 33.4439 13.8281C33.8939 13.8281 34.3039 13.9081 34.6639 14.0781C35.0239 14.2381 35.3339 14.4681 35.5739 14.7581C35.8239 15.0481 36.0139 15.3881 36.1439 15.7881C36.2739 16.1881 36.3439 16.6181 36.3439 17.0881C36.3439 17.5581 36.2739 18.0081 36.1239 18.4081C35.9739 18.8081 35.7739 19.1581 35.5039 19.4481C35.2439 19.7381 34.9239 19.9681 34.5639 20.1281C34.1939 20.2881 33.7939 20.3781 33.3639 20.3781C32.9639 20.3781 32.6139 20.3181 32.3239 20.1981C32.0339 20.0781 31.8139 19.9281 31.6639 19.7481V22.6281H29.7539V22.6381ZM34.4739 17.1081C34.4739 16.8481 34.4339 16.6181 34.3639 16.4281C34.2939 16.2381 34.1839 16.0781 34.0439 15.9381C33.9139 15.8081 33.7539 15.7081 33.5839 15.6481C33.4139 15.5881 33.2339 15.5581 33.0539 15.5581C32.8739 15.5581 32.6939 15.5881 32.5139 15.6481C32.3339 15.7081 32.1939 15.8081 32.0639 15.9381C31.9339 16.0681 31.8339 16.2281 31.7539 16.4281C31.6739 16.6181 31.6439 16.8481 31.6439 17.1081C31.6439 17.3681 31.6839 17.5981 31.7539 17.7881C31.8339 17.9781 31.9339 18.1381 32.0639 18.2781C32.1939 18.4181 32.3439 18.5081 32.5139 18.5781C32.6839 18.6481 32.8639 18.6781 33.0539 18.6781C33.2439 18.6781 33.4139 18.6481 33.5839 18.5881C33.7539 18.5281 33.9139 18.4281 34.0439 18.2981C34.1739 18.1681 34.2839 18.0081 34.3639 17.8081C34.4439 17.6081 34.4739 17.3781 34.4739 17.1181V17.1081Z"
        fill="#183028"
      />
    </Svg>
  );
}
