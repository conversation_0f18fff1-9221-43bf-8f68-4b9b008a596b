import React, { useState } from 'react';
import { Dimensions, Modal } from 'react-native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { Button, H2 } from 'cube-ui-components';
import { BlurView } from 'expo-blur';

const { width: windowWidth } = Dimensions.get('window');

const CenterView = styled.View(() => ({
  justifyContent: 'center',
  width: windowWidth - sizes[4] * 2,
  height: 400,
  maxWidth: 380,
  borderRadius: sizes[4],
  padding: sizes[6],
  borderWidth: 5,
  borderColor: colors.alertRed,
}));

const Container = styled(BlurView)(() => ({
  flex: 1,
  backgroundColor: 'rgba(24, 48, 40, 0.7)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const Spacer = styled.View(({ height }: { height: number }) => ({
  height,
}));

type ModalProps = {
  reTake: () => void;
  onExitRole: () => void;
};

const MissionFailModal = ({ reTake, onExitRole }: ModalProps) => {
  const [modalVisible, setModalVisible] = useState(true);
  const { t } = useTranslation('ecoach');

  const tryAgain = () => {
    setModalVisible(false);
    reTake();
  };

  return (
    <Modal animationType="slide" transparent={true} visible={modalVisible}>
      <Container tint="extraLight" intensity={30}>
        <CenterView>
          <H2
            fontWeight="bold"
            color={'#F60000'}
            style={{ textAlign: 'center' }}>
            {t('missFail')}
          </H2>
          <Spacer height={sizes[12]} />
          <Button
            variant={'secondary'}
            onPress={tryAgain}
            text={t('tryAgain')}
          />

          <Spacer height={sizes[4]} />
          <Button
            variant={'tertiary'}
            onPress={onExitRole}
            text={t('exitRole')}
          />
        </CenterView>
      </Container>
    </Modal>
  );
};

export default MissionFailModal;
