import { useQueryClient } from '@tanstack/react-query';
import { requestQuotation } from 'api/quotationApi';
import { CubePictogramIcon, LargeBody, Row, Toast } from 'cube-ui-components';
import { differenceInCalendarDays, format, isAfter, parse } from 'date-fns';
import useSalesIllustationResolver, {
  ContextType,
} from 'features/proposal/validations/useSalesIllustationResolver';
import useBoundStore from 'hooks/useBoundStore';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { getCaseKey, useGetCaseManually } from 'hooks/useGetCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetQuotationManually } from 'hooks/useGetQuotation';
import {
  createElement,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useForm, UseFormProps } from 'react-hook-form';
import RootSiblingsManager from 'react-native-root-siblings';
import { Case, CaseStatus } from 'types/case';
import { PartyRole, PartyType } from 'types/party';
import {
  BasicConfig,
  Funds,
  Insured,
  PlanConfig,
  Quotation,
  QuotationResponse,
  Reminder,
  ReminderCode,
  RiderCode,
  Warning,
  WarningCode,
} from 'types/quotation';
import { appVersion } from 'utils/context';
import { compareDatesByMonthAndDay } from 'utils/helper/dateUtil';
import { dateTimeFormatUtil } from 'utils/helper/formatUtil';
import {
  FundValidationFeedback,
  SiFormValues,
  SiNextButtonAction,
} from '../types';
import { getFormDefaultValues } from '../untils/getFormDefaultValues';
import useBasePlanDefinition from './useBasePlanDefinition';

import { AxiosError } from 'axios';
import { useProductListQuery } from 'features/productSelection/hooks/useProducts';
import { t, TFuncKey } from 'i18next';
import { getConfig } from 'screens/SalesIllustrationForm/config';
import { Product } from 'types/products';
import { CubeResponse } from 'types/response';
import { countryModuleSiConfig } from 'utils/config/module';
import { country } from 'utils/context';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { useRiderPlanInfo } from './rider/useRiderPlanInfo';
import useBasePlanFormValues from './useBasePlanFormValues';
import useBasePlanInfo from './useBasePlanInfo';
import useFundsDefinitions from './useFundsDefinitions';
import {
  useInitQuotationCalculation,
  useQuotationCalculation,
} from './useQuotationCalculation';
import useQuotationInsureds from './useQuotationInsureds';
import useRiderDefinitions from './useRiderDefinitions';
import { useUpsertQuotation } from './useUpsertQuotation';
import { useTheme } from '@emotion/react';
import { Platform } from 'react-native';

const DUPLICATED_NAME = 'quotation name already exists';

type useQuotationProps = UseFormProps<SiFormValues, ContextType> & {
  pid: string;
  isSyncTopUp?: boolean;
  hasFnaData?: boolean;
  lockMainInsuredToProposer?: boolean;
  beforeQuotationCalculation?: () => void;
};

/**
 * This hook will do below things:
 * 1. Get case
 * 2. Instantiate react hook form
 * 3. Determine resume or initialize quotation
 * 4. Prepare & tidy up quotation-related variables & methods
 * 5. Handle errors from backend
 */
export default function useQuotationForm({
  pid,
  isSyncTopUp,
  lockMainInsuredToProposer,
  beforeQuotationCalculation,
  hasFnaData,
  ...formProps
}: useQuotationProps) {
  const logPrefix = `💴[QuotationForm,${format(new Date(), 'HHmmssSSSS')}]`;

  const { colors, typography, space } = useTheme();

  const queryClient = useQueryClient();

  const { isTabletMode } = useLayoutAdoptionCheck();

  const { data: agentProfile } = useGetAgentProfile();
  const { data: optionList } = useGetOptionList();

  // Store
  const caseId = useBoundStore(state => state.case.caseId);
  const quotationId = useBoundStore(state => state.quotation?.quotationId);
  const quotationName = useBoundStore(state => state.quotation?.quotationName);
  const rawQuotation = useBoundStore(state => state.quotation?.rawQuotation);
  const rpqResult = useBoundStore(state => state.quotation?.rpqResult);
  const riskPrefValidationResult = useBoundStore(
    state => state.quotation?.riskPrefValidationResult,
  );
  const alterations = useBoundStore(state => state.quotation?.alterations);
  const isAlterationsUpdated = useBoundStore(
    state => state.quotation?.isAlterationsUpdated,
  );
  const isSavedQuotation = useBoundStore(
    state => state.quotation?.isSavedQuotation,
  );
  const quotationStoreErrors = useBoundStore(state => state.quotation?.errors);
  const quotationStoreReminders = useBoundStore(
    state => state.quotation?.reminders,
  );

  const quotationCriticalErrors = useBoundStore(
    state => state.quotation?.criticalErrors,
  );
  const isVul = useBoundStore(state => state.quotation?.isVul);

  const isQuickSi = useBoundStore(state => state.quotation?.isQuickSi);
  const isPdfViewed = useBoundStore(state => state.quotation?.isPdfViewed);
  const updateQutationStore = useBoundStore(
    state => state.quotationActions.update,
  );
  const resetQutationStore = useBoundStore(
    state => state.quotationActions.reset,
  );
  const updateIsPdfViewed = useBoundStore(
    state => state.quotationActions.updateIsPdfViewed,
  );

  // Hooks that use store
  const { insureds, proposers, isEntityFlow } = useQuotationInsureds();
  const basePlanInfo = useBasePlanInfo();
  const basePlanDefinition = useBasePlanDefinition();
  const riderPlanInfo = useRiderPlanInfo();
  const riderPlanDefinitions = useRiderDefinitions();
  const fundsDefinitions = useFundsDefinitions();

  const [isInitializingQuotation, setIsInitializingQuotation] = useState(true);
  const [initQuotationError, setInitQuotationError] = useState(false);

  const [toastError, setToastError] = useState<RootSiblingsManager>();
  const [toastReminder, setToastReminder] = useState<RootSiblingsManager>();

  const [showBirthdayReminder, setShowBirthdayReminder] = useState(false);
  const siConfig = getConfig(isEntityFlow, isQuickSi, hasFnaData);

  // useState
  const [activeCase, setActiveCase] = useState<Case | null>(null);
  const [activeQuotation, setActiveQuotation] = useState<Quotation | null>(
    null,
  );
  const [isResumeQuotation, setIsResumeQuotation] = useState(false);

  const [reminderModal, setReminderModal] = useState<{
    visible: boolean;
    title: string;
    description: string;
    triggered?: boolean;
  }>({
    visible: false,
    title: '',
    description: '',
    triggered: false,
  });

  const [ignoreFormDirty, setIgnoreFormDirty] = useState<boolean>(false);
  const [isEditing, setEditing] = useState<boolean>(false);

  /* using same footer button for either send si pdf via email or procceed to next flow  */
  const [nextButtonAction, setNextButtonAction] = useState<SiNextButtonAction>(
    siConfig.isSendEmailRequiredToProceed
      ? SiNextButtonAction.SEND_EMAIL
      : SiNextButtonAction.VIEW_PDF,
  );

  const { mutateAsync: getProductDetail } = useProductListQuery();

  const {
    mutateAsync: getCase,
    error: getCaseError,
    isIdle: isGetCaseInitial,
  } = useGetCaseManually();
  const { mutateAsync: getQuotation } = useGetQuotationManually();

  const handleOnShowBirthdayReminder = (insureds: Insured[]) => {
    const closeToBirthday = insureds.some(i => {
      const input =
        i?.dob && parse(i?.dob.toString(), 'yyyy-MM-dd', new Date());
      if (input) {
        const now = new Date();
        const today = new Date(
          now.getFullYear(),
          now.getMonth(),
          now.getDate(),
          0,
          0,
          0,
        );

        const comingBirthday = new Date(
          today.getFullYear(),
          input.getMonth(),
          input.getDate(),
          0,
          0,
          0,
        );

        if (isAfter(today, comingBirthday)) {
          comingBirthday.setFullYear(today.getFullYear() + 1);
        }

        return (
          differenceInCalendarDays(comingBirthday, today) <= 7 &&
          differenceInCalendarDays(comingBirthday, today) >= 1
        );
      }
    });
    setShowBirthdayReminder(closeToBirthday);
  };

  const setFormDefaultValues = async (response: QuotationResponse) => {
    const { values, reinitFields } = getFormDefaultValues({
      response,
      isResumed: !!quotationId,
    });
    form.reset(values);

    if (Array.isArray(reinitFields) && reinitFields.length > 0) {
      console.log(
        `${logPrefix} some fields are different from the existing quotation, reinit the quotation calculation...`,
      );
      await form.trigger(reinitFields as Array<keyof SiFormValues>);
    }
  };

  const initSaleIllustrationForm = async () => {
    console.log(`${logPrefix} start the SI form initialization...`);

    if (!caseId) {
      console.error(`${logPrefix} case ID is not provided`);
      setInitQuotationError(true);
      return;
    }

    let aCase: Case | null = null;
    let selectedProduct: Product | null = null;
    try {
      aCase = await getCase(caseId);
    } catch (ex) {
      console.error(
        `${logPrefix} fail to get case with case ID: ${caseId}`,
        ex,
      );
      setInitQuotationError(true);
      return;
    }

    setActiveCase(aCase);

    try {
      const [productInfo] = await getProductDetail({ pid });
      selectedProduct = productInfo ?? null;
    } catch (ex) {
      console.error(
        `${logPrefix} fail to get product info with PID: ${pid}`,
        ex,
      );
      setInitQuotationError(true);
      return;
    }

    setIsResumeQuotation(
      !aCase || !!aCase.quotations?.find(q => q.id === quotationId),
    );

    let aQuotation: Quotation | undefined = undefined;
    if (quotationId) {
      console.log(`${logPrefix} resume the quotation with ID: `, quotationId);
      try {
        aQuotation = await getQuotation({ caseId, quotationId });
        setActiveQuotation(aQuotation);
      } catch (ex) {
        console.error(
          `${logPrefix} fail to query the existing quotation ID: ${quotationId}`,
          ex,
        );
        setInitQuotationError(true);
        return;
      }
    }

    const response = await initQuotationCalculation({
      agentProfile: agentProfile,
      caseData: aCase,
      productId: pid,
      rcdDate: selectedProduct?.rcdDate,
      existingQuotation: aQuotation,
      relationshipList: optionList?.RELATIONSHIP?.options,
      occupationList: optionList?.OCCUPATION?.options,
    });

    // TODO: will remove the country check after all region return warnings correctly
    if (country === 'ib' && response.warnings?.length === 0) {
      setIgnoreFormDirty(true);
    }
    // end of TODO

    // remove the init warnings if the quotation is not resumed
    if (!aQuotation) {
      response.warnings = undefined;
    }

    updateQutationStore(response);

    handleOnShowBirthdayReminder(response.quotation?.insureds ?? []);

    console.log(`${logPrefix} initQuotationCalculation response:\n`, response);

    const isEntityFlow =
      response.quotation?.proposers?.length > 0 &&
      response.quotation?.proposers[0]?.clientType?.toUpperCase() ===
        PartyType.ENTITY.toUpperCase();
    const siConfig = getConfig(
      isEntityFlow,
      response.quotation?.isQuickSI,
      hasFnaData,
    );
    setNextButtonAction(
      siConfig.isSendEmailRequiredToProceed
        ? SiNextButtonAction.SEND_EMAIL
        : SiNextButtonAction.VIEW_PDF,
    );

    await setFormDefaultValues(response);
  };

  const toggleAgeChangeDialog = useCallback(() => {
    if (rawQuotation && activeQuotation && !reminderModal.triggered) {
      rawQuotation.insureds.map(i => {
        if (
          compareDatesByMonthAndDay(
            new Date(activeQuotation?.updatedAt),
            new Date(i?.dob as string),
          ) &&
          compareDatesByMonthAndDay(new Date(i?.dob as string), new Date())
        ) {
          setReminderModal({
            visible: true,
            title: t('proposal:reminder.title'),
            description: t('proposal:notify.changeAge.description'),
            triggered: true,
          });
        }
      });
    }
  }, [activeQuotation, rawQuotation, reminderModal.triggered]);

  const closeReminderDialog = useCallback(() => {
    setShowBirthdayReminder(false);
  }, [setShowBirthdayReminder]);

  // Init
  useEffect(() => {
    setIsInitializingQuotation(true);
    initSaleIllustrationForm().finally(() => setIsInitializingQuotation(false));
  }, []);

  // ========
  // Quotation
  const {
    mutateAsync: initQuotationCalculation,
    reset: resetInitQuotationCalculation,
    isError: initQuotationCalculationError,
  } = useInitQuotationCalculation({
    lockMainInsuredToProposer,
  });
  const {
    mutateAsync: mutateQuotationCalculation,
    reset: resetQuotationCalculation,
  } = useQuotationCalculation();

  // useMemo
  const isValidCaseStatus = useMemo(
    () =>
      activeCase?.status?.every(
        s => s !== CaseStatus.APP_SUBMITTED && s !== CaseStatus.IN_APP,
      ) ?? true,
    [activeCase],
  );

  const isValidQuotationCalculation = useMemo(
    () =>
      !(Array.isArray(quotationStoreErrors) && quotationStoreErrors.length > 0),
    [quotationStoreErrors],
  );

  const owner = useMemo(
    () =>
      activeCase?.parties?.find(p =>
        p.roles.some(v => v === PartyRole.PROPOSER),
      ),
    [activeCase?.parties],
  );

  const insured = useMemo(
    () =>
      activeCase?.parties?.find(p =>
        p.roles.some(v => v === PartyRole.INSURED),
      ),
    [activeCase?.parties],
  );

  const defaultQuotationName = useMemo(() => {
    const existingName = activeQuotation?.quotationName;
    const productName = basePlanInfo?.productName?.en;

    if (existingName) return existingName;
    if (!productName) return '';

    const quickSiSuffix = isQuickSi ? ' Quick' : '';
    return `${productName} ${dateTimeFormatUtil(new Date())}${quickSiSuffix}`;
  }, [activeQuotation, basePlanInfo, isQuickSi]);

  // useCallback
  const quoteAction = useCallback(
    async (siFormValues: SiFormValues) => {
      if (!rawQuotation || !basePlanDefinition) return;

      updateIsPdfViewed(false);
      // https://tkdodo.eu/blog/mastering-mutations-in-react-query#some-callbacks-might-not-fire
      // use following method to make sure onSuccess is called once. The previous mutation will be cancelled
      return mutateQuotationCalculation(
        {
          quotation: rawQuotation,
          parties: {
            proposers,
            insureds,
          },
          basePlanDefinition: basePlanDefinition,
          riderPlanDefinitions: riderPlanDefinitions,
          siFormValues,
          rpqResult,
        },
        {
          onSuccess: updateQutationStore,
        },
      );
    },
    [
      rawQuotation,
      basePlanDefinition,
      updateIsPdfViewed,
      mutateQuotationCalculation,
      proposers,
      insureds,
      riderPlanDefinitions,
      rpqResult,
      updateQutationStore,
    ],
  );

  const {
    upsertQuotation: upsertQuotationFunction,
    isLoading: isUpsertingQuotation,
    isError: isUpsertQuotationError,
    createQuotationError,
  } = useUpsertQuotation();

  const isDuplicateCreation =
    createQuotationError instanceof AxiosError &&
    createQuotationError.response &&
    createQuotationError.response.data &&
    'messageList' in createQuotationError.response.data &&
    (
      createQuotationError.response.data as CubeResponse<null>
    ).messageList?.find(({ content }) => content.includes(DUPLICATED_NAME));

  const upsertQuotation = useCallback(
    async (
      quotationName: string,
      isNewVersion: boolean,
      isSelected?: boolean,
      fundsPieChartImage?: string,
    ) => {
      if (!caseId || !rawQuotation) {
        return;
      }
      const quotationId = await upsertQuotationFunction(
        caseId,
        { ...rawQuotation, fundsPieChartImage },
        quotationName,
        {
          isNewVersion,
          isSelected,
        },
      );
      return quotationId;
    },
    [caseId, rawQuotation, upsertQuotationFunction],
  );

  const saveSelectedQuotation = useCallback(
    async ({
      quotationName,
      fundsPieChartImage,
      feedback,
    }: {
      quotationName: string;
      fundsPieChartImage?: string;
      feedback?: FundValidationFeedback;
    }) => {
      if (!caseId || !rawQuotation) {
        return;
      }

      const finalInsureds = !Array.isArray(riderPlanInfo)
        ? insureds
        : insureds.filter(i => {
            if (i.insuredId === owner?.id) {
              return true;
            }
            if (i.isMainInsured) {
              return true;
            }

            return riderPlanInfo.some(r => r?.insuredId === i.insuredId);
          });

      let fundsSettings: Funds | undefined = rawQuotation.funds;

      if (
        countryModuleSiConfig?.fund?.fillUnselectedFund &&
        rawQuotation.funds
      ) {
        const currentFundAllocations = rawQuotation.funds.fundAllocation ?? [];
        const currentFundIds = currentFundAllocations.map(f => f?.fid);
        fundsSettings = rawQuotation.funds
          ? {
              ...rawQuotation.funds,
              // ONLY perform on the selected quotation for eApp stage.
              // fill in all funds with 0 allocation, even if the funds are not added in the original quotation
              fundAllocation: [
                ...fundsDefinitions
                  .filter(fd => !currentFundIds?.includes(fd.fid)) // filter the funds that are added in the original quotation
                  .map(fd => ({
                    ...fd,
                    allocation: 0,
                    topUp: 0,
                  })),
                ...currentFundAllocations,
              ],
            }
          : undefined;
      }

      const finalQuotationResponse = await requestQuotation({
        quotation: {
          ...rawQuotation,
          insureds: finalInsureds,
          funds: fundsSettings,
          fundValidationFeedback: feedback,
        },
        appVersion,
      });

      return await upsertQuotationFunction(
        caseId,
        { ...finalQuotationResponse.quotation, fundsPieChartImage },
        quotationName,
        {
          isNewVersion: false,
          isSelected: true,
        },
      );
    },
    [
      caseId,
      rawQuotation,
      riderPlanInfo,
      insureds,
      fundsDefinitions,
      upsertQuotationFunction,
      owner?.id,
    ],
  );

  // ========
  // Form
  const { handleSubmit, ...form } = useForm<SiFormValues, ContextType>({
    mode: 'onSubmit',
    reValidateMode: 'onSubmit',
    shouldFocusError: false,
    criteriaMode: 'all',
    resolver: useSalesIllustationResolver,
    context: {
      isVul,
      quoteAction,
      basePlanDefinition,
      isSyncTopUp,
      proposers,
    },
    defaultValues: {
      basePlan: {},
      riderPlans: [],
      fundsAllocation: [],
    },
    ...formProps,
  });

  const isLockFormAction = useMemo(() => {
    // prevent get pdf when field is still being edited
    if (isEditing || form.formState.isValidating) return true;

    // check the init and calculation error
    if (
      initQuotationError ||
      initQuotationCalculationError ||
      !isValidQuotationCalculation
    ) {
      return true;
    }

    // check the hook form errors
    if (Object.keys(form.formState.errors).length > 0) {
      return true;
    }

    // only check the form dirty if init and calculation are passed
    if (!ignoreFormDirty && !form.formState.isSubmitted && !isResumeQuotation) {
      return true;
    }

    return false;
  }, [
    isEditing,
    isValidQuotationCalculation,
    initQuotationError,
    initQuotationCalculationError,

    form.formState.errors,
    form.formState.isValidating,

    ignoreFormDirty,
    form.formState.isSubmitted,
    isResumeQuotation,
  ]);

  const isCalculatingQuotation = useMemo(
    () => isGetCaseInitial || form.formState.isSubmitting,
    [form.formState.isSubmitting, isGetCaseInitial],
  );

  const onStartInput = () => setEditing(true);
  const onEndInput = () => setEditing(false);

  const triggerQuotationCalculation = useCallback(() => {
    beforeQuotationCalculation?.();

    handleSubmit(
      data => {
        console.log(
          `${logPrefix} detect the SI form submit is called and validation is passed\n`,
          data,
        );
        return;
      },
      errors => {
        console.log(
          `${logPrefix} detect the SI form submit is called and validation is failed \n`,
          errors,
        );
      },
    )();
  }, [handleSubmit, logPrefix]);

  /* display error message
    MYS: sticky error message that can be dismissed manually
    PH: normal toast message
  */

  const handleCustomErrors = useCallback(
    (
      quotationStoreErrors: Warning[] | undefined,
      quotationStoreReminders: Reminder[] | undefined,
    ) => {
      let error: RootSiblingsManager | undefined;
      let reminder: RootSiblingsManager | undefined;

      // error
      if (toastError) {
        Toast.hide(toastError);
      }

      if (Array.isArray(quotationStoreErrors)) {
        /*  filter out the errors that are:
          -  custom errors  
          -  not categorized  
          -  for base plan field but not found in base plan config
       */
        const customErrors = quotationStoreErrors.filter(
          ({ code, id, label }) =>
            (!code ||
              code === WarningCode.CUSTOM_ERROR ||
              (!basePlanDefinition?.planConfig?.[id as keyof PlanConfig] &&
                !basePlanDefinition?.basicConfig?.[id as keyof BasicConfig] &&
                code)) &&
            !!label?.en,
        );
        if (customErrors.length) {
          error = Toast.show(
            customErrors.map(customError => ({
              message: renderLabelByLanguage(customError?.label) ?? '',
            })),

            {
              showDismiss: isTabletMode,
              type: 'errorBottom',
              duration: isTabletMode ? undefined : Toast.durations.LONG,
            },
          );
        }
      }

      // reminder
      if (toastReminder) {
        Toast.hide(toastReminder);
      }

      if (Array.isArray(quotationStoreReminders)) {
        /*  only keep reminders that:
          -  have type = CUSTOM_INFO
          -  have label  
       */
        const customReminders = quotationStoreReminders.filter(
          ({ code, label }) => code === ReminderCode.CUSTOM_INFO && !!label?.en,
        );
        if (customReminders.length) {
          const messageTypo = isTabletMode
            ? typography.largeBody
            : typography.largeLabel;
          const toast = Toast.show(
            customReminders.map(customReminder => ({
              IconLeft: <CubePictogramIcon.CashGrowth width={52} height={52} />,
              message: renderLabelByLanguage(customReminder?.label) ?? '',
              messageStyle: {
                fontSize: messageTypo.size,
                lineHeight: messageTypo.lineHeight,
              },
            })),

            {
              showDismiss: true,
              type: 'info',
              duration: undefined,
              containerStyle: {
                height: 'auto',
                paddingVertical: space[4],
                bottom:
                  (Platform.OS === 'ios' ? space[10] : space[4]) +
                  (error ? 70 + space[4] : 0),
              },
            },
          );

          setToastReminder(toast);
        }
      }

      if (error) {
        setToastError(error);
      }

      if (reminder) {
        setToastReminder(reminder);
      }
    },
    [toastError, toastReminder],
  );

  useEffect(() => {
    handleCustomErrors(quotationStoreErrors, quotationStoreReminders);
  }, [quotationStoreErrors, quotationStoreReminders]);

  useEffect(() => {
    return () => {
      console.log(
        `${logPrefix} clean up the quotation calculation and reset the store`,
      );
      resetInitQuotationCalculation();
      resetQuotationCalculation();
      resetQutationStore();
      queryClient.invalidateQueries({
        queryKey: getCaseKey(),
        refetchType: 'all',
      });
    };
  }, []);

  useEffect(() => {
    if (alterations && isAlterationsUpdated) {
      console.log(
        `${logPrefix} set the alterations to form value and trigger the quotation call\n`,
        alterations,
      );
      form.setValue('alterations', alterations, {
        shouldValidate: true,
      });
    }
  }, [isAlterationsUpdated]);

  useEffect(() => {
    if (isSavedQuotation && quotationId && quotationName) {
      console.log(
        `${logPrefix} set the quotation ID and quotation name to form value and trigger the quotation call\n`,
        quotationId,
        quotationName,
      );
      form.setValue('quotationName', quotationName, {
        shouldValidate: false,
      });
      form.setValue('quotationId', quotationId, {
        shouldValidate: false,
      });
    }
  }, [isSavedQuotation]);

  useEffect(() => {
    const notes = basePlanDefinition?.properties?.notes;
    if (notes && !reminderModal?.triggered) {
      setReminderModal({
        visible: true,
        title: t('proposal:reminder.title'),
        description: notes,
        triggered: true,
      });
    }
  }, [basePlanDefinition?.properties?.notes, reminderModal?.triggered]);

  useBasePlanFormValues({
    setValue: form.setValue,
    getValues: form.getValues,
    pid,
  });

  const isAllRidersMandatory = riderPlanDefinitions.every(r => r.mandatory);

  const monthlyIncomePlan = form
    .watch('riderPlans')
    .find(plan => plan?.pid === RiderCode.ADIA);

  // check if user had accepted the policies for monthly income rider
  const isUserDisaggreeMonthlyPlanPolicies = Boolean(
    monthlyIncomePlan &&
      (!monthlyIncomePlan.constent1 || !monthlyIncomePlan.constent2),
  );

  const isLockSubmitButton = !isPdfViewed || isUserDisaggreeMonthlyPlanPolicies;
  const isEntity = owner?.clientType === PartyType.ENTITY;

  const nextButtonLabel = useMemo(() => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL) {
      return t('proposal:sendPdf');
    }

    if (!siConfig.flow.label) {
      return t('proposal:startApplication');
    }

    return t(siConfig.flow.label as TFuncKey);
  }, [siConfig, nextButtonAction]);

  const nextButtonSubLabel = useMemo(() => {
    if (nextButtonAction === SiNextButtonAction.SEND_EMAIL)
      return t('proposal:benefitIllustrationPDF');

    return undefined;
  }, [nextButtonAction]);

  return {
    form,
    quotationId,
    activeQuotation,
    isQuickSi,
    isEntity,
    defaultQuotationName,
    basePlanInfo: {
      ...basePlanInfo,
      owner,
      insured,
    },
    basePlanDefinition,
    riderPlanDefinitions,
    isAllRidersMandatory,
    fundsDefinitions,
    rpqResult,
    riskPrefValidationResult,

    isValidCaseStatus,
    isResumeQuotation,
    onStartInput,
    onEndInput,
    isLockFormAction,
    isLockSubmitButton,
    ignoreFormDirty,

    isInitializingQuotation,

    // quotation calculation
    isCalculatingQuotation,
    isValidQuotationCalculation,

    // upsert quotation
    isUpsertingQuotation,
    isUpsertQuotationError,

    errors: {
      getCaseError,
      criticalErrors: quotationCriticalErrors,
      initError: initQuotationError || initQuotationCalculationError,
      duplicateError: isDuplicateCreation,
    },
    showBirthdayReminder,

    // next button controller
    nextButtonAction,
    setNextButtonAction,
    nextButtonLabel,
    nextButtonSubLabel,
    siConfig,

    // Methods
    upsertQuotation,
    saveSelectedQuotation,
    triggerQuotationCalculation,
    closeReminderDialog,
    toggleAgeChangeDialog,
    setReminderModal,
    reminderModal,
  };
}
