import { useTheme } from '@emotion/react';
import { useDeleteParty, useSaveParty } from 'hooks/useParty';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  EAppState,
  useEAppStore,
} from 'features/eAppV2/common/utils/store/eAppStore';
import useBoundStore from 'hooks/useBoundStore';
import { useUpdateCase } from 'hooks/useUpdateCase';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useState, useMemo, useCallback } from 'react';
import { PartyRole, PartyType } from 'types/party';
import { shallow } from 'zustand/shallow';
import { toParty } from '../utils/caseUtils';

const useBeneficialOwnerInfoLogic = () => {
  const { space, colors } = useTheme();
  const {
    clientType,
    hasBeneficialOwner,
    setHasBeneficialOwner,
    beneficialOwnerIsUSCitizen,
    setBeneficialOwnerIsUSCitizen,
    beneficialOwnerPersonalInfo: data,
    updateBeneficialOwnerPersonalInfo: setData,
  } = useEAppStore(
    state => ({
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
      hasBeneficialOwner: state.hasBeneficialOwner,
      setHasBeneficialOwner: state.setHasBeneficialOwner,
      beneficialOwnerIsUSCitizen:
        state.beneficialOwnerPersonalInfo.beneficialOwnerIsUSCitizen,
      setBeneficialOwnerIsUSCitizen: state.setBeneficialOwnerIsUSCitizen,
      beneficialOwnerPersonalInfo: state.beneficialOwnerPersonalInfo,
      updateBeneficialOwnerPersonalInfo:
        state.updateBeneficialOwnerPersonalInfo,
    }),
    shallow,
  );

  const isEntity = clientType === PartyType.ENTITY;
  const [dialogVisible, setDialogVisible] = useState(false);
  const next = useEAppProgressBarStore(state => state.next);
  const isActionDisabled = useMemo(() => {
    return (
      hasBeneficialOwner &&
      !isEntity &&
      (!data.personalDetails.done ||
        !data.addressInfo.done ||
        !data.contactDetails.done ||
        !data.nationalityDetails.done)
    );
  }, [data, hasBeneficialOwner, isEntity]);

  const caseId = useBoundStore(state => state.case.caseId);
  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const { saveOcrImage, isLoading: isSavingOcr } = useSaveOcrImage();
  const { mutateAsync: deleteParty, isLoading: isDeletingParty } =
    useDeleteParty();
  const { mutateAsync: updateCase, isLoading: isUpdatingCase } =
    useUpdateCase();

  const onChangeHasBeneficialOwner = (value: boolean) => {
    setHasBeneficialOwner(value);
    if (isEntity && !value) {
      setBeneficialOwnerIsUSCitizen(false);
    }
  };

  const onSubmit = async (
    savingData?: EAppState['beneficialOwnerPersonalInfo'],
  ) => {
    savingData ||= data;
    if (caseId) {
      if (hasBeneficialOwner) {
        await updateCase({ caseId, haveBeneficialOwner: true });
        const id = await saveParty(
          toParty({ role: PartyRole.BENEFICIAL_OWNER, info: savingData }),
        );
        if (id) {
          const { fileName, filePath } = await saveOcrImage(
            id,
            savingData.personalDetails.document.frontImage,
            PartyRole.BENEFICIAL_OWNER,
          );
          if (fileName && filePath) {
            setData({
              id,
              personalDetails: {
                ...savingData.personalDetails,
                document: {
                  ...savingData.personalDetails.document,
                  frontImage: {
                    name: fileName,
                    thumbnail: filePath,
                    base64:
                      savingData.personalDetails.document.frontImage.base64,
                    fromOcr: true,
                  },
                },
              },
            });
          } else {
            setData({ id });
          }
        }
      } else {
        await updateCase({ caseId, haveBeneficialOwner: false });
        if (savingData.id) {
          await deleteParty({ caseId, partyId: savingData.id });
        }
      }
    }
    next();
  };

  const onSave = useCallback(
    async (savingData?: EAppState['beneficialOwnerPersonalInfo']) => {
      savingData ||= data;
      if (caseId) {
        if (hasBeneficialOwner) {
          await updateCase({ caseId, haveBeneficialOwner: true });
          const id = await saveParty(
            toParty({ role: PartyRole.BENEFICIAL_OWNER, info: savingData }),
          );
          if (id) {
            const { fileName, filePath } = await saveOcrImage(
              id,
              savingData.personalDetails.document.frontImage,
              PartyRole.BENEFICIAL_OWNER,
            );
            if (fileName && filePath) {
              setData({
                id,
                personalDetails: {
                  ...savingData.personalDetails,
                  document: {
                    ...savingData.personalDetails.document,
                    frontImage: {
                      name: fileName,
                      thumbnail: filePath,
                      base64:
                        savingData.personalDetails.document.frontImage.base64,
                        fromOcr: true,
                    },
                  },
                },
              });
            } else {
              setData({ id });
            }
          }
        } else {
          await updateCase({ caseId, haveBeneficialOwner: false });
          if (savingData.id) {
            await deleteParty({ caseId, partyId: savingData.id });
          }
        }
      }
    },
    [
      caseId,
      data,
      deleteParty,
      hasBeneficialOwner,
      saveOcrImage,
      saveParty,
      setData,
      updateCase,
    ],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return {
    space,
    colors,
    clientType,
    hasBeneficialOwner,
    setHasBeneficialOwner,
    beneficialOwnerIsUSCitizen,
    setBeneficialOwnerIsUSCitizen,
    data,
    setData,
    isEntity,
    dialogVisible,
    setDialogVisible,
    next,
    isActionDisabled,
    caseId,
    saveParty,
    isSavingParty,
    saveOcrImage,
    isSavingOcr,
    deleteParty,
    isDeletingParty,
    updateCase,
    isUpdatingCase,
    onChangeHasBeneficialOwner,
    onSubmit,
    isNarrowScreen,
    loading: isSavingParty || isDeletingParty || isUpdatingCase || isSavingOcr,
    onSave,
  };
};

export default useBeneficialOwnerInfoLogic;

export type BeneficialOwnerInfoLogic = ReturnType<
  typeof useBeneficialOwnerInfoLogic
>;
