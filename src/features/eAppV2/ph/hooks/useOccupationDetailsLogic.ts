import { useTheme } from '@emotion/react';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { UseFormReturn } from 'react-hook-form';
import { OccupationDetailsForm } from '../validations/applicationDetails/sections/occupationDetailsValidation';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import useJuvenile from './useJuvenile';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { PartyType } from 'types/party';

const useOccupationDetailsLogic = ({
  value,
  onDone,
  form,
}: {
  value: OccupationDetailsForm;
  onDone: (values: OccupationDetailsForm, done?: boolean) => void;
  form: UseFormReturn<OccupationDetailsForm>;
}) => {
  const theme = useTheme();
  const { t } = useTranslation(['eApp']);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const { data: optionList, isFetching: isFetchingOptionList } =
    useGetOptionList();
  const { clientType, entityName } = useEAppStore(state => {
    return {
      clientType: state.policyOwnerPersonalInfo.personalDetails.customerType,
      entityName: state.policyOwnerPersonalInfo.entityDetails.entityName,
    };
  });

  const isJuvenile = useJuvenile();

  const openOccupationDetails = () => {
    setTooltipVisible(true);
  };

  const itemKey = useEAppProgressBarStore(state => state.itemKey);

  const {
    control,
    watch,
    handleSubmit,
    setValue,
    formState: { isValid },
  } = form;

  useEffect(() => {
    if (itemKey === 'insured' && isJuvenile) {
      setValue('occupationType', '0005');
    }
  }, [itemKey, isJuvenile]);

  useEffect(() => {
    if (clientType === PartyType.ENTITY) {
      setValue('nameOfEmployer', entityName);
    }
  }, [clientType, entityName]);

  const submit = useCallback(
    (data: OccupationDetailsForm) => {
      onDone(data);
    },
    [onDone],
  );

  const occupationOptions = useMemo(() => {
    if (clientType === PartyType.ENTITY) {
      return optionList?.OCCUPATION_GROUP_ENTITY.options || [];
    }
    return optionList?.OCCUPATION_GROUP.options || [];
  }, [clientType, optionList]);

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return {
    theme,
    t,
    tooltipVisible,
    setTooltipVisible,
    optionList,
    isFetchingOptionList,
    clientType,
    entityName,
    isJuvenile,
    openOccupationDetails,
    itemKey,
    submit,
    occupationOptions,
    isNarrowScreen,
    form,
    onDone,
  };
};

export default useOccupationDetailsLogic;

export type OccupationDetailsLogic = ReturnType<
  typeof useOccupationDetailsLogic
>;
