import {
  CORRESPONDENCE_ADDRESS_OPTION,
  MY_COUNTRY,
  MY_ELECTRONIC_COPY,
  MY_EMAIL_CONTACT_MODE,
  MY_ENGLISH_LANGUAGE,
  MY_MOBILE_CODE,
  NEW_ADDRESS_OPTION,
} from 'constants/optionList';
import {
  invalidFormatMessage,
  requiredMessage,
} from 'features/eAppV2/common/constants/eAppErrorMessages';
import { formPhoneNumber } from 'utils/validation/customValidation';
import {
  InferType, boolean, object,
  ref,
  string
} from 'yup';
import {
  documents,
  normalizedSpaceString,
  nullableDate,
} from '../../../common/validations/eAppCommonSchema';
import { mysAddressSchema, mysAnnualIncomeAmount, mysIdNumber, mysName, occupationDetailRequired } from '../commonSchema';
import { CHANNELS } from 'types/channel';
import { MAX_EMAIL_LENGTH } from 'features/coverageDetails/validation/common/constant';

export type PolicyOwnerFormSchemaType = InferType<
  typeof policyOwnerFormValidationSchema
>;
export type PolicyOwnerFormSchemaKey = keyof PolicyOwnerFormSchemaType;

export const initialPolicyOwnerFormData: PolicyOwnerFormSchemaType = {
  //propose participants
  title: '',
  smokingHabit: '',
  maritalStatus: '',
  relationship: '',
  dob: null,
  primaryIdType: '',
  additionalIDType: '',
  source: '',
  fullName: '',
  gender: '',
  ethnicity: '',
  religion: '',
  identificationNumber: '',
  additionalIdentification: '',
  //nationality details
  nationality: MY_COUNTRY,
  countryOfBirth: '',
  stateOfBirth: '',
  cityOfBirth: '',
  cityName: '',
  //occupation details
  occupation: '',
  occupationGroup: '',
  occupationDescription: '',
  nameOfBusiness: '',
  exactDuties: '',
  annualIncomeAmount: '',
  natureOfWork: '',
  annualIncome: '',
  taxPurpose: '',
  //contact details
  email: '',
  mobileCountryCode: MY_MOBILE_CODE,
  mobileNumber: '',
  homeCountryCode: MY_MOBILE_CODE,
  homeNumber: '',
  officeCountryCode: MY_MOBILE_CODE,
  officeNumber: '',
  preferredCertificateCopy: MY_ELECTRONIC_COPY,
  preferredLanguage: MY_ENGLISH_LANGUAGE,
  preferredContactMode: MY_EMAIL_CONTACT_MODE,
  //address information
  correspondenceAddress: NEW_ADDRESS_OPTION,
  correspondenceAddressLine1: '',
  correspondenceAddressLine2: '',
  correspondenceAddressLine3: '',
  correspondencePostCode: '',
  correspondenceCity: '',
  correspondenceState: '',
  correspondenceCountry: '',
  residentialAddress: CORRESPONDENCE_ADDRESS_OPTION,
  residentialAddressLine1: '',
  residentialAddressLine2: '',
  residentialAddressLine3: '',
  residentialPostCode: '',
  residentialCity: '',
  residentialState: '',
  residentialCountry: '',
  businessAddress: '',
  businessAddressLine1: '',
  businessAddressLine2: '',
  businessAddressLine3: '',
  businessPostCode: '',
  businessCity: '',
  businessState: '',
  businessCountry: '',
  isF2F: true,
  isBancaSourceOfFundWealthRequired: false,
  sourceOfFund: '',
  sourceOfPremium: '',
  sourceOfPremiumCountry: '',
  documents: [],
};

export const policyOwnerFormRequiredSchema = {
  id: string(),
  title: string().required(requiredMessage),
  smokingHabit: string().required(requiredMessage),
  relationship: string(),
  maritalStatus: string().required(requiredMessage),
  dob: nullableDate(),
  primaryIdType: string().required(requiredMessage),
  additionalIDType: string(),
  source: string().required(requiredMessage),
  fullName: mysName().required(requiredMessage),
  gender: string().required(requiredMessage),
  ethnicity: string().required(requiredMessage),
  religion: string().required(requiredMessage),
  identificationNumber: mysIdNumber('primaryIdType', 'dob', 'gender').required(
    requiredMessage,
  ),
  additionalIdentification: normalizedSpaceString().test(
    'required-additional-id',
    requiredMessage,
    (value, ctx) => {
      const additionalIdType = ctx.resolve(ref('additionalIDType'));
      if (additionalIdType) return Boolean(value);
      return true;
    },
  ),
  nationality: string().required(requiredMessage),
  countryOfBirth: string().required(requiredMessage),
  stateOfBirth: string().required(requiredMessage),
  cityOfBirth: string().required(requiredMessage),
  cityName: normalizedSpaceString().required(requiredMessage),
  occupation: string().required(requiredMessage),
  occupationGroup: string(),
  occupationDescription: string().required(requiredMessage),
  nameOfBusiness: occupationDetailRequired('occupationGroup'),
  exactDuties: occupationDetailRequired('occupationGroup'),
  annualIncome: occupationDetailRequired('occupationGroup'),
  annualIncomeAmount: mysAnnualIncomeAmount(
    'annualIncome',
    'annualIncomeAmount',
  ),
  natureOfWork: occupationDetailRequired('occupationGroup'),
  taxPurpose: string().required(requiredMessage),
  email: normalizedSpaceString()
    .trim()
    .lowercase()
    .max(MAX_EMAIL_LENGTH, invalidFormatMessage)
    .email(invalidFormatMessage)
    .required(requiredMessage),
  mobileCountryCode: string().required(requiredMessage),
  mobileNumber: formPhoneNumber('mobileCountryCode').required(requiredMessage),
  homeCountryCode: string(),
  homeNumber: formPhoneNumber('homeCountryCode'),
  officeCountryCode: string(),
  officeNumber: formPhoneNumber('officeCountryCode'),
  preferredCertificateCopy: string().required(requiredMessage),
  preferredLanguage: string().required(requiredMessage),
  preferredContactMode: string().required(requiredMessage),
  ...mysAddressSchema(
    ['correspondence', 'residential', 'business'],
    'occupationGroup',
  ),
  isF2F: boolean(),
  documents: documents(),
  isBancaSourceOfFundWealthRequired: boolean(),
  sourceOfFund: bancaRequired(),
  sourceOfPremium: bancaRequired(),
  sourceOfPremiumCountry: bancaRequired(),
  agentChannel: string(),
  typeOfCustomer: string().test(
    'required',
    requiredMessage,
    (typeOfCustomer, ctx) => {
      const agentChannel = ctx.resolve(ref('agentChannel'));
      if (agentChannel === CHANNELS.BANCA) {
        return Boolean(typeOfCustomer);
      }
      return true;
    },
  ),
  staffId: string().test('required', requiredMessage, (staffId, ctx) => {
    const agentChannel = ctx.resolve(ref('agentChannel'));
    const typeOfCustomer = ctx.resolve(ref('typeOfCustomer'));
    if (agentChannel === CHANNELS.BANCA) {
      return Boolean(staffId) || typeOfCustomer === 'NA';
    }
    return true;
  }),
} as const;

function bancaRequired() {
  return string().when('isBancaSourceOfFundWealthRequired', {
    is: true,
    then: schema => schema.required(requiredMessage),
    otherwise: schema => schema.optional(),
  });
}

export type PolicyOwnerRequiredSchemaKey =
  keyof typeof policyOwnerFormRequiredSchema;

export const requiredPolicyOwnerFields = Object.keys(
  policyOwnerFormRequiredSchema,
) as PolicyOwnerRequiredSchemaKey[];

export const policyOwnerFormValidationSchema = object({
  ...policyOwnerFormRequiredSchema,
});

export const policyOwnerAddressInfoSchema = object({
  ...mysAddressSchema(
    ['correspondence', 'residential', 'business'],
    'occupationGroup',
  ),
  occupationGroup: string(),
});
