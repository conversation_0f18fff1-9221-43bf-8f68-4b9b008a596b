import { useTheme } from '@emotion/react';
import { Button, Row } from 'cube-ui-components';
import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useBypassEAppValidation } from 'features/eApp/hooks/useBypassEAppValidation';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export default function FormAction({
  hasShadow = true,
  primaryDisabled,
  primaryLoading,
  onPrimaryPress,
  primaryLabel = 'Next',
  primarySublabel,
  secondaryDisabled,
  secondaryLoading,
  secondaryLabel,
  onSecondaryPress,
  style,
  children,
}: {
  hasShadow?: boolean;
  primaryDisabled?: boolean;
  primaryLoading?: boolean;
  primaryLabel?: string;
  primarySublabel?: string;
  onPrimaryPress: () => void;
  secondaryDisabled?: boolean;
  secondaryLoading?: boolean;
  secondaryLabel?: string;
  onSecondaryPress?: () => void;
  style?: StyleProp<ViewStyle>;
  children?: React.ReactNode;
}) {
  const { space, colors } = useTheme();
  const bypassValidation = useBypassEAppValidation(
    state => state.bypassValidation,
  );

  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();

  return (
    <SafeAreaView
      edges={['bottom']}
      style={[
        {
          paddingHorizontal: space[isNarrowScreen ? 3 : 4],
          paddingTop: space[4],
          paddingBottom: space[4],
          backgroundColor: colors.background,
          borderTopWidth: hasShadow ? 1 : 0,
          borderColor: colors.palette.fwdGrey[100],
          gap: space[4],
        },
        style,
      ]}>
      {children}
      <Row gap={space[4]} justifyContent="center">
        {secondaryLabel && (
          <Button
            variant="secondary"
            loading={secondaryLoading}
            disabled={secondaryDisabled && !bypassValidation}
            onPress={onSecondaryPress}
            text={secondaryLabel}
            style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
          />
        )}
        <Button
          loading={primaryLoading}
          disabled={primaryDisabled && !bypassValidation}
          onPress={onPrimaryPress}
          text={primaryLabel}
          subtext={primarySublabel}
          style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
        />
      </Row>
    </SafeAreaView>
  );
}
