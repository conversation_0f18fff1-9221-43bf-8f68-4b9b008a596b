import { Style<PERSON><PERSON> } from 'react-native';
import { MessageExtensionLinkId } from 'agent-guru';
import { ViewStyle } from 'react-native';
import AddNewLeadForm from 'features/lead/tablet/components/AddNewLeadForm';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

export interface PreviewProps {
  Component: JSX.Element;
  containerStyle?: StyleProp<ViewStyle>;
  contentStyle?: StyleProp<ViewStyle>;
}

export default function useLinkPreview() {
  const { isTabletMode } = useLayoutAdoptionCheck();

  function getPreviewProps(
    linkId: MessageExtensionLinkId,
  ): PreviewProps | null {
    if (linkId === MessageExtensionLinkId.LEAD_FORM) {
      return {
        Component: (
          <AddNewLeadForm
            onClose={function () {
              // nothing -- for eslint
            }}
            setLeadType={function () {
              // nothing -- for eslint
            }}
          />
        ),
        containerStyle: {
          transform: 'scale(0.25)',
        },
        contentStyle: {
          width: isTabletMode ? 800 : 990,
        },
      };
    }

    return null;
  }

  return { getPreviewProps };
}
