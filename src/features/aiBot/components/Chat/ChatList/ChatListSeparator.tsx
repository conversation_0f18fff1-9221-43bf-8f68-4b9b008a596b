import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import { colors } from 'cube-ui-components/dist/cjs/theme/base';
import { Message, formatDateWithTime } from 'agent-guru';
import { View } from 'react-native';

const Container = styled(View)(({ theme }) => ({
  marginTop: theme.space[4],
  marginBottom: theme.space[3],
}));

const DateText = styled(Typography.SmallLabel)(() => ({
  color: colors.fwdDarkGreen[50],
  textAlign: 'center',
}));

interface ChatBubbleSeparatorI {
  messages: Message[];
  leadingMessage: Message;
}

const INTERVAL = 60 * 10;

export function ChatListSeparator({
  messages,
  leadingMessage,
}: ChatBubbleSeparatorI) {
  const leadingItemIndex = messages.findIndex(
    m => m.id === leadingMessage.id && m.userType === leadingMessage.userType,
  );

  const nextMessage = messages[leadingItemIndex + 1];

  const leadingMessageDate = leadingMessage.datetime
    ? new Date(leadingMessage.datetime)
    : null;
  const nextMessageDate = nextMessage.datetime
    ? new Date(nextMessage.datetime)
    : null;
  if (!leadingMessageDate || !nextMessageDate) return <></>;

  // Check if time elapsed is greater than X
  // If so, we display next message date time
  const timeDiff =
    (nextMessageDate.getTime() - leadingMessageDate.getTime()) / 1000; //in ms

  if (Math.round(timeDiff) < INTERVAL) {
    return <></>;
  }

  return (
    <Container style={{ marginVertical: 12 }}>
      <DateText>{formatDateWithTime(nextMessageDate)}</DateText>
    </Container>
  );
}
