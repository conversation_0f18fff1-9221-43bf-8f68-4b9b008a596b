import { FlatList } from 'react-native';
import React, { useCallback, useEffect, useMemo } from 'react';
import { getYear } from 'date-fns';

export const NUMBER_OF_YEARS_PER_ROW = 4;

const useScrollToYear = ({
  controlledDate,
  years,
  listRef,
}: {
  controlledDate?: Date;
  years: number[];
  listRef: React.RefObject<FlatList>;
}) => {
  const scrollIndex = useMemo(() => {
    const selectedYear = controlledDate ? getYear(controlledDate) : 0;
    const yearNth = years.findIndex(year => year === selectedYear) + 1;

    const rowNth = Math.floor(yearNth / NUMBER_OF_YEARS_PER_ROW);

    return rowNth;
  }, []);

  const scrollToIndex = useCallback(() => {
    setTimeout(() => {
      listRef.current?.scrollToIndex({
        index: scrollIndex,
        animated: true,
        viewPosition: 0.5,
      });
    });
  }, [scrollIndex, years]);

  useEffect(() => {
    scrollToIndex();
  }, [scrollIndex, years]);

  return {
    scrollIndex,
    scrollToIndex,
  };
};

export default useScrollToYear;
